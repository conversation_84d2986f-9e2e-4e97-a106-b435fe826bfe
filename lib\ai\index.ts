import { wrapLanguageModel } from "ai";
import { customMiddleware } from "./custom-middleware";
import { models } from "./dev-models";
import { createGeon } from "@ai-sdk/geon";
import { openai } from "@ai-sdk/openai";
import { createDifyProvider } from "dify-ai-provider";

export const geon = createGeon({
  baseURL: process.env.API_BASE_URL || "http://121.163.19.104:8002/v1",
  apiKey: process.env.API_KEY || "123"
});

// Dify provider 인스턴스 생성
export const difyProvider = createDifyProvider({
  baseURL: process.env.DIFY_URL || "https://ai-dify.geon.kr/v1",
});

export const customModel = (modelId: string) => {
  return wrapLanguageModel({
    model: geon(modelId),
    middleware: customMiddleware,
  });
};

export const difyModel = (modelId: string) => {
  const model = models.find(m => m.id === modelId);
  if (!model) {
    throw new Error(`Model not found: ${modelId}`);
  }

  const difyModelInstance = difyProvider(model.apiIdentifier, {
    apiKey: model.apiKey,
    responseMode: "streaming",
  });

  return wrapLanguageModel({
    model: difyModelInstance,
    middleware: customMiddleware,
  });
};

export const openaiModel = (modelId: string) => {
  return wrapLanguageModel({
    model: openai(modelId),
    middleware: customMiddleware,
  });
};
