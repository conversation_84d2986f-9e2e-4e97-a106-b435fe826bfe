{"name": "chrome86-test", "version": "1.0.0", "description": "Chrome 86 테스트 환경", "scripts": {"test:chrome86": "node test-chrome86.js", "test:interactive": "node -e \"console.log('Chrome 86 테스트 환경 준비됨!'); console.log('VNC 접속: http://localhost:7900'); console.log('Selenium Hub: http://localhost:4444'); require('child_process').spawn('node', ['-p'], {stdio: 'inherit'});\""}, "dependencies": {"selenium-webdriver": "^4.0.0"}}