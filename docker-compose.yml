services:
  # 기존 lx-chatbot 서비스
  lx-chatbot:
    image: lx-chatbot/frontend:latest
    container_name: lx-chatbot-frontend
    restart: unless-stopped
    environment:
      # 공유재산 관리 어시스턴트 설정
      - DIFY_API_KEY=${DIFY_API_KEY}
      - DIFY_URL=${DIFY_URL}
    ports:
      - "3002:3000"

  # Chrome 86 테스트 환경
  chrome86:
    image: selenium/standalone-chrome:86.0 # 실제 존재하는 태그
    container_name: chrome86-test
    ports:
      - "4444:4444"
      - "7900:7900"
    shm_size: 2gb
    environment:
      - SE_VNC_NO_PASSWORD=1
      - SE_SCREEN_WIDTH=1920
      - SE_SCREEN_HEIGHT=1080
    volumes:
      - ./test-results:/home/<USER>/test-results
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped

  test-runner:
    image: node:18-alpine
    container_name: chrome86-test-runner
    working_dir: /workspace
    volumes:
      - ./:/workspace
      - ./test-results:/workspace/test-results
    environment:
      - CHROME_URL=http://chrome86:4444/wd/hub
      - TARGET_URL=http://host.docker.internal:3000
    depends_on:
      - chrome86
    command: tail -f /dev/null
    restart: unless-stopped