services:
  # 기존 lx-chatbot 서비스
  lx-chatbot:
    image: lx-chatbot/frontend:latest
    container_name: lx-chatbot-frontend
    restart: unless-stopped
    environment:
      # 공유재산 관리 어시스턴트 설정
      - DIFY_API_KEY=${DIFY_API_KEY}
      - DIFY_URL=${DIFY_URL}
    ports:
      - "3002:3000"

  # Chrome 86 테스트 환경 (VNC 포함) - 대안 1
  chrome86:
    image: selenium/standalone-chrome-debug:3.141.59-20201010  # Chrome 86 + VNC 확실히 포함
    container_name: chrome86-test
    ports:
      - "4444:4444"  # Selenium WebDriver
      - "5900:5900"  # VNC 접속 (debug 이미지는 5900 포트 사용)
    shm_size: 2gb
    environment:
      - SCREEN_WIDTH=1920
      - SCREEN_HEIGHT=1080
    volumes:
      - ./test-results:/tmp/test-results
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
