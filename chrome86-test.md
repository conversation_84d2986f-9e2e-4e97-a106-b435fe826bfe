# Chrome 86 테스트 환경 사용법

## 🚀 시작하기

### 1. 환경 실행
```bash
# Chrome 86 테스트 환경 시작
docker-compose up -d chrome86 test-runner

# 로그 확인
docker-compose logs -f chrome86
```

### 2. 호스트 PC에서 개발 서버 실행
```bash
# 별도 터미널에서 Next.js 개발 서버 실행
pnpm dev
# 또는
npm run dev
```

## 🧪 테스트 방법

### 방법 1: 자동 테스트 스크립트
```bash
# 테스트 컨테이너에서 실행
docker-compose exec test-runner sh -c "cd /workspace && npm install --package-lock-only && npm install selenium-webdriver && node test-chrome86.js"
```

### 방법 2: VNC로 직접 브라우저 조작
1. 브라우저에서 http://localhost:7900 접속
2. Chrome 86 브라우저가 화면에 나타남
3. 주소창에 `http://host.docker.internal:3000` 입력
4. 개발자 도구(F12) 열고 콘솔에서 테스트:
   ```javascript
   // structuredClone 지원 확인
   typeof structuredClone
   
   // 실제 사용 테스트
   const obj = { a: 1, b: { c: 2 } };
   const cloned = structuredClone(obj); // Chrome 86에서는 에러 발생
   ```

### 방법 3: Selenium WebDriver로 프로그래밍 테스트
```javascript
const { Builder } = require('selenium-webdriver');

const driver = await new Builder()
  .forBrowser('chrome')
  .usingServer('http://localhost:4444/wd/hub')
  .build();

await driver.get('http://host.docker.internal:3000');
const result = await driver.executeScript('return typeof structuredClone');
console.log('structuredClone 지원:', result); // "undefined" 출력됨
```

## 🔍 확인 포인트

### Chrome 86에서 지원하지 않는 기능들
- `structuredClone()` - Chrome 98+에서 지원
- `Array.prototype.at()` - Chrome 92+에서 지원  
- `Object.hasOwn()` - Chrome 93+에서 지원
- `Array.prototype.findLast()` - Chrome 97+에서 지원

### 테스트 결과 확인
- 스크린샷: `test-results/chrome86-test.png`
- 콘솔 로그에서 지원 여부 확인
- VNC 화면에서 실시간 확인

## 🛠️ 유용한 명령어

```bash
# Chrome 86 컨테이너만 재시작
docker-compose restart chrome86

# 테스트 결과 초기화
rm -rf test-results/*.png

# 컨테이너 상태 확인
docker-compose ps

# 전체 환경 종료
docker-compose down
```

## 🌐 접속 URL
- **VNC (브라우저 화면)**: http://localhost:7900
- **Selenium Hub**: http://localhost:4444
- **개발 서버**: http://localhost:3000 (호스트 PC)
- **컨테이너에서 개발 서버**: http://host.docker.internal:3000

## 💡 팁
- VNC 화면에서 마우스 우클릭으로 컨텍스트 메뉴 사용 가능
- 개발자 도구는 F12 또는 Ctrl+Shift+I로 열기
- 브라우저 새로고침은 F5 또는 Ctrl+R
- 화면 해상도는 docker-compose.yml에서 조정 가능
