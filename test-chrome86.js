const { Builder, By, until } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');

async function testChrome86() {
  console.log('🚀 Chrome 86 테스트 시작...');
  
  // Chrome 86 컨테이너에 연결
  const options = new chrome.Options();
  options.addArguments('--no-sandbox');
  options.addArguments('--disable-dev-shm-usage');
  
  const driver = await new Builder()
    .forBrowser('chrome')
    .setChromeOptions(options)
    .usingServer('http://chrome86:4444/wd/hub')
    .build();

  try {
    console.log('📱 브라우저 연결 성공!');
    
    // 호스트 PC의 localhost:3000에 접속
    const targetUrl = 'http://host.docker.internal:3000';
    console.log(`🌐 ${targetUrl} 접속 중...`);
    
    await driver.get(targetUrl);
    await driver.sleep(2000);
    
    // 페이지 제목 확인
    const title = await driver.getTitle();
    console.log(`📄 페이지 제목: ${title}`);
    
    // Chrome 버전 확인
    const userAgent = await driver.executeScript('return navigator.userAgent');
    console.log(`🔍 User Agent: ${userAgent}`);
    
    // structuredClone 지원 여부 확인
    const structuredCloneSupport = await driver.executeScript(`
      try {
        return {
          supported: typeof structuredClone !== 'undefined',
          type: typeof structuredClone,
          error: null
        };
      } catch (e) {
        return {
          supported: false,
          type: 'undefined',
          error: e.message
        };
      }
    `);
    
    console.log('🧪 structuredClone 테스트 결과:');
    console.log(`   지원 여부: ${structuredCloneSupport.supported}`);
    console.log(`   타입: ${structuredCloneSupport.type}`);
    if (structuredCloneSupport.error) {
      console.log(`   에러: ${structuredCloneSupport.error}`);
    }
    
    // 실제 structuredClone 사용 테스트
    if (structuredCloneSupport.supported) {
      const cloneTest = await driver.executeScript(`
        try {
          const original = { a: 1, b: { c: 2 } };
          const cloned = structuredClone(original);
          return {
            success: true,
            original: original,
            cloned: cloned,
            equal: JSON.stringify(original) === JSON.stringify(cloned)
          };
        } catch (e) {
          return {
            success: false,
            error: e.message
          };
        }
      `);
      
      console.log('🔬 structuredClone 실행 테스트:');
      console.log(`   성공: ${cloneTest.success}`);
      if (cloneTest.success) {
        console.log(`   복제 성공: ${cloneTest.equal}`);
      } else {
        console.log(`   에러: ${cloneTest.error}`);
      }
    }
    
    // 스크린샷 저장
    const screenshot = await driver.takeScreenshot();
    require('fs').writeFileSync('/workspace/test-results/chrome86-test.png', screenshot, 'base64');
    console.log('📸 스크린샷 저장: test-results/chrome86-test.png');
    
    console.log('✅ 테스트 완료!');
    
  } catch (error) {
    console.error('❌ 테스트 실패:', error.message);
  } finally {
    await driver.quit();
  }
}

// 스크립트 직접 실행시
if (require.main === module) {
  testChrome86().catch(console.error);
}

module.exports = { testChrome86 };
