# LX-Chatbot

Next.js 15 기반의 공유재산 관리 어시스턴트입니다. AI를 활용한 공유재산 관리, 상담, 정보 제공 서비스를 제공합니다.

## 🚀 기술 스택

- **Framework:** Next.js 15 (App Router)
- **Runtime:** Node.js 20.18 LTS 이상
- **Package Manager:** pnpm
- **Database:** Drizzle ORM
- **Styling:** Tailwind CSS, shadcn/ui
- **Container:** Docker

## 🔧 시작하기

### 환경 설정

1. 프로젝트 클론

```bash
git clone [repository-url]
cd lx-chatbot
```

2. 환경 변수 설정

`.env.sample` 파일을 `.env.local`로 복사하여 필요한 환경 변수를 설정합니다.

```bash
cp .env.sample .env.local
```

### 개발 환경 실행

```bash
# 의존성 설치
pnpm install

# 개발 서버 실행
pnpm dev
```

## 📝 코딩 컨벤션

- **명명 규칙:** 케밥 케이스(kebab-case) 사용
- **상태 관리:** 전역 상태는 `app/providers.tsx`에서 관리
- **컴포넌트:** 재사용 가능한 컴포넌트는 `components/` 디렉토리에 위치
- **스타일링:** Tailwind CSS 클래스를 사용. shadcn/ui 컴포넌트는 `components/` 디렉토리에 위치

## 📄 설정 파일

- `components.json`: shadcn/ui 설정
- `drizzle.config.ts`: Drizzle ORM 설정
- `next.config.mjs`: Next.js 설정
- `postcss.config.mjs`: PostCSS 설정
- `tailwind.config.ts`: Tailwind CSS 설정
- `tsconfig.json`: TypeScript 설정

## 🏢 주요 기능

- **공유재산 관리 상담**: AI 기반 공유재산 관리 관련 질의응답
- **법령 및 규정 안내**: 공유재산 관련 법령, 규정, 절차 안내
- **업무 프로세스 지원**: 공유재산 관리 업무 프로세스 가이드
- **문서 작성 지원**: 공유재산 관련 문서 작성 도움
- **데이터 분석**: 공유재산 현황 및 통계 정보 제공

## 📌 주의사항

- Node.js 20.18 이상 버전이 필요합니다.
- 환경 변수 설정 없이는 정상적인 실행이 불가능합니다.
- 챗봇 이용을 위해 Dify API 키 발급이 필요합니다.